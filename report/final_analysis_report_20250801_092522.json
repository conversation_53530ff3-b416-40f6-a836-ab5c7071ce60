{"summary": {"total_samples": 50, "analysis_timestamp": "2025-08-01T09:25:23.419228", "analyzed_fields": ["system", "prompt"], "language_distribution": {"total_count": 50, "proportions": {"zh": {"count": 50, "proportion": 1.0}}, "most_common": ["zh", 50], "insight": "样本全部为zh类型。"}, "avg_text_length": 262.69, "content_diversity": 28.41, "domain_insights": "无法确定具体领域类型，建议进行数据质量检查和针对性预处理。"}, "boxplot_stats": {"system": {"char_count": {"count": 50, "mean": 221.5, "std": 196.4609, "min": 0.0, "q1": 164.25, "median": 200.0, "q3": 200.0, "max": 874.0, "iqr": 35.75, "lower_bound": 110.625, "upper_bound": 253.625, "lower_outlier_count": 8, "upper_outlier_count": 5, "insight": "字符数(反映样本信息密度和完整性)：有8个样本少于111字符；有5个样本超过254字符"}, "alpha_ratio": {"count": 50, "mean": 0.7413, "std": 0.1968, "min": 0.0, "q1": 0.7216, "median": 0.7869, "q3": 0.8463, "max": 1.0, "iqr": 0.1247, "lower_bound": 0.5346, "upper_bound": 1.0332, "lower_outlier_count": 5, "upper_outlier_count": 0, "insight": "字母占比(反映文本中字母字符的占比分布)：字母占比分布明显左偏，存在较多低字母占比样本，可能主要是中文内容"}, "digit_ratio": {"count": 50, "mean": 0.0593, "std": 0.1049, "min": 0.0, "q1": 0.0, "median": 0.0344, "q3": 0.09, "max": 0.6923, "iqr": 0.09, "lower_bound": -0.135, "upper_bound": 0.225, "lower_outlier_count": 0, "upper_outlier_count": 1, "insight": "数字占比(反映文本中数值信息的占比密度)：数字占比变化幅度极大，可能混合了纯数字数据和纯文本内容；数字占比分布明显右偏，存在较多高数字占比样本，可能混合了数值数据；有1个样本数字含量超过22.5%"}, "alnum_ratio": {"count": 50, "mean": 0.8005, "std": 0.1806, "min": 0.0, "q1": 0.81, "median": 0.8376, "q3": 0.8676, "max": 1.0, "iqr": 0.0576, "lower_bound": 0.7236, "upper_bound": 0.9539, "lower_outlier_count": 5, "upper_outlier_count": 3, "insight": "字母数字占比(反映文本中字母数字字符的总体占比)：数据分布明显左偏，存在较多异常低值样本"}, "valid_ratio": {"count": 50, "mean": 0.8005, "std": 0.1806, "min": 0.0, "q1": 0.81, "median": 0.8376, "q3": 0.8676, "max": 1.0, "iqr": 0.0576, "lower_bound": 0.7236, "upper_bound": 0.9539, "lower_outlier_count": 5, "upper_outlier_count": 3, "insight": "有效字符比例(反映文本清洁度和噪声水平)：存在有效字符低于85.0%的情况；有效字符普遍偏低，整体文本质量不佳，存在较多噪声"}, "word_count": {"count": 50, "mean": 120.96, "std": 107.6216, "min": 0.0, "q1": 89.0, "median": 106.0, "q3": 115.75, "max": 484.0, "iqr": 26.75, "lower_bound": 48.875, "upper_bound": 155.875, "lower_outlier_count": 8, "upper_outlier_count": 5, "insight": "词数(反映样本信息量和内容丰富度)：有8个样本少于49个词；有5个样本词数超过156词"}, "stopword_count": {"count": 50, "mean": 13.68, "std": 13.9319, "min": 0.0, "q1": 8.0, "median": 11.0, "q3": 15.0, "max": 65.0, "iqr": 7.0, "lower_bound": -2.5, "upper_bound": 25.5, "lower_outlier_count": 0, "upper_outlier_count": 5, "insight": "停用词数(反映文本中功能词的使用频率)：数据变化幅度较大，样本差异明显；数据分布明显右偏，存在较多异常高值样本；有5个样本停用词超过26个"}, "avg_word_length": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "sentence_count": {"count": 50, "mean": 11.88, "std": 10.5539, "min": 0.0, "q1": 8.0, "median": 11.0, "q3": 12.0, "max": 48.0, "iqr": 4.0, "lower_bound": 2.0, "upper_bound": 18.0, "lower_outlier_count": 8, "upper_outlier_count": 5, "insight": "句子数(反映样本结构完整性和语义连贯性)：有8个样本句子数少于2句；有5个样本句子数超过18句"}, "line_count": {"count": 50, "mean": 2.32, "std": 1.9742, "min": 0.0, "q1": 1.0, "median": 2.0, "q3": 2.0, "max": 8.0, "iqr": 1.0, "lower_bound": -0.5, "upper_bound": 3.5, "lower_outlier_count": 0, "upper_outlier_count": 10, "insight": "行数(反映文本格式结构和排版方式)：有10个样本行数超过4行"}, "avg_sentence_length": {"count": 50, "mean": 18.9098, "std": 7.5777, "min": 0.0, "q1": 16.1875, "median": 18.1951, "q3": 21.2917, "max": 50.0, "iqr": 5.1042, "lower_bound": 8.5312, "upper_bound": 28.9479, "lower_outlier_count": 4, "upper_outlier_count": 3, "insight": "平均句子长度(反映句子结构复杂度和断句质量)：有句子长度低于正常范围下限10.0词的样本，可能存在过度断句或句子结构过于简单；有句子长度超过正常范围上限30.0词的样本，可能存在断句失败或长复合句"}, "avg_line_length": {"count": 50, "mean": 112.4817, "std": 84.7006, "min": 0.0, "q1": 49.25, "median": 99.5, "q3": 162.75, "max": 505.0, "iqr": 113.5, "lower_bound": -121.0, "upper_bound": 333.0, "lower_outlier_count": 0, "upper_outlier_count": 1, "insight": "平均行长度(反映文本格式规范性和排版质量)：有1个样本平均行长度超过333.0字符"}, "bigram_repetition": {"count": 50, "mean": 0.2696, "std": 0.1565, "min": 0.0, "q1": 0.2034, "median": 0.2575, "q3": 0.3407, "max": 0.677, "iqr": 0.1372, "lower_bound": -0.0025, "upper_bound": 0.5465, "lower_outlier_count": 0, "upper_outlier_count": 4, "insight": "2-gram重复度(反映文本局部重复程度和内容多样性)：有2-gram重复度超过40.0%的样本，存在严重重复内容"}, "trigram_repetition": {"count": 50, "mean": 0.1628, "std": 0.1211, "min": 0.0, "q1": 0.0957, "median": 0.144, "q3": 0.2058, "max": 0.5207, "iqr": 0.1101, "lower_bound": -0.0694, "upper_bound": 0.3709, "lower_outlier_count": 0, "upper_outlier_count": 4, "insight": "3-gram重复度(反映文本短语重复程度和表达多样性)：有3-gram重复度超过30.0%的样本，短语重复度过高"}, "mtld_score": {"count": 42, "mean": 28.4466, "std": 5.893, "min": 17.4, "q1": 24.575, "median": 28.0714, "q3": 31.375, "max": 45.3333, "iqr": 6.8, "lower_bound": 14.375, "upper_bound": 41.575, "lower_outlier_count": 0, "upper_outlier_count": 2, "insight": "MTLD词汇多样性(反映文本词汇丰富度和表达复杂性)：有词汇多样性低于30.0的样本；词汇多样性普遍不足，可能是重复文本或词汇表达有限"}}, "prompt": {"char_count": {"count": 50, "mean": 303.88, "std": 257.3629, "min": 0.0, "q1": 234.5, "median": 300.0, "q3": 300.0, "max": 1369.0, "iqr": 65.5, "lower_bound": 136.25, "upper_bound": 398.25, "lower_outlier_count": 9, "upper_outlier_count": 5, "insight": "字符数(反映样本信息密度和完整性)：有9个样本少于136字符；有5个样本超过398字符"}, "alpha_ratio": {"count": 50, "mean": 0.7389, "std": 0.2111, "min": 0.0, "q1": 0.7249, "median": 0.7891, "q3": 0.8178, "max": 1.0, "iqr": 0.0929, "lower_bound": 0.5854, "upper_bound": 0.9572, "lower_outlier_count": 5, "upper_outlier_count": 3, "insight": "字母占比(反映文本中字母字符的占比分布)：字母占比分布明显左偏，存在较多低字母占比样本，可能主要是中文内容"}, "digit_ratio": {"count": 50, "mean": 0.055, "std": 0.0505, "min": 0.0, "q1": 0.0, "median": 0.0481, "q3": 0.0867, "max": 0.1767, "iqr": 0.0867, "lower_bound": -0.13, "upper_bound": 0.2167, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "alnum_ratio": {"count": 50, "mean": 0.7938, "std": 0.2133, "min": 0.0, "q1": 0.8238, "median": 0.8467, "q3": 0.8633, "max": 1.0, "iqr": 0.0395, "lower_bound": 0.7645, "upper_bound": 0.9226, "lower_outlier_count": 5, "upper_outlier_count": 3, "insight": "字母数字占比(反映文本中字母数字字符的总体占比)：数据分布明显左偏，存在较多异常低值样本"}, "valid_ratio": {"count": 50, "mean": 0.7938, "std": 0.2133, "min": 0.0, "q1": 0.8238, "median": 0.8467, "q3": 0.8633, "max": 1.0, "iqr": 0.0395, "lower_bound": 0.7645, "upper_bound": 0.9226, "lower_outlier_count": 5, "upper_outlier_count": 3, "insight": "有效字符比例(反映文本清洁度和噪声水平)：存在有效字符低于85.0%的情况；有效字符普遍偏低，整体文本质量不佳，存在较多噪声"}, "word_count": {"count": 50, "mean": 163.58, "std": 135.8499, "min": 0.0, "q1": 130.0, "median": 158.0, "q3": 168.0, "max": 722.0, "iqr": 38.0, "lower_bound": 73.0, "upper_bound": 225.0, "lower_outlier_count": 9, "upper_outlier_count": 5, "insight": "词数(反映样本信息量和内容丰富度)：有9个样本少于73个词；有5个样本词数超过225词"}, "stopword_count": {"count": 50, "mean": 20.52, "std": 17.1619, "min": 0.0, "q1": 14.0, "median": 18.0, "q3": 24.75, "max": 84.0, "iqr": 10.75, "lower_bound": -2.125, "upper_bound": 40.875, "lower_outlier_count": 0, "upper_outlier_count": 5, "insight": "停用词数(反映文本中功能词的使用频率)：有5个样本停用词超过41个"}, "avg_word_length": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "sentence_count": {"count": 50, "mean": 16.36, "std": 12.7354, "min": 0.0, "q1": 12.25, "median": 15.0, "q3": 18.0, "max": 62.0, "iqr": 5.75, "lower_bound": 3.625, "upper_bound": 26.625, "lower_outlier_count": 9, "upper_outlier_count": 5, "insight": "句子数(反映样本结构完整性和语义连贯性)：有9个样本句子数少于4句；有5个样本句子数超过27句"}, "line_count": {"count": 50, "mean": 2.3, "std": 2.0224, "min": 0.0, "q1": 1.0, "median": 1.0, "q3": 3.0, "max": 10.0, "iqr": 2.0, "lower_bound": -2.0, "upper_bound": 6.0, "lower_outlier_count": 0, "upper_outlier_count": 3, "insight": "行数(反映文本格式结构和排版方式)：数据分布极度右偏，存在极端高值样本严重拉高分布；有3个样本行数超过6行"}, "avg_sentence_length": {"count": 50, "mean": 17.1764, "std": 6.3139, "min": 0.0, "q1": 15.9528, "median": 17.6312, "q3": 20.125, "max": 35.0, "iqr": 4.1722, "lower_bound": 9.6944, "upper_bound": 26.3833, "lower_outlier_count": 6, "upper_outlier_count": 2, "insight": "平均句子长度(反映句子结构复杂度和断句质量)：有句子长度低于正常范围下限10.0词的样本，可能存在过度断句或句子结构过于简单；有句子长度超过正常范围上限30.0词的样本，可能存在断句失败或长复合句"}, "avg_line_length": {"count": 50, "mean": 157.4721, "std": 115.7779, "min": 0.0, "q1": 61.775, "median": 110.5833, "q3": 283.75, "max": 415.0, "iqr": 221.975, "lower_bound": -271.1875, "upper_bound": 616.7125, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "平均行长度(反映文本格式规范性和排版质量)：数据分布极度右偏，存在极端高值样本严重拉高分布"}, "bigram_repetition": {"count": 50, "mean": 0.3419, "std": 0.1797, "min": 0.0, "q1": 0.2509, "median": 0.382, "q3": 0.4573, "max": 0.6893, "iqr": 0.2063, "lower_bound": -0.0586, "upper_bound": 0.7668, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "2-gram重复度(反映文本局部重复程度和内容多样性)：有2-gram重复度超过40.0%的样本，存在严重重复内容；2-gram重复度分布明显左偏，存在较多低重复样本，可能有大量原创内容"}, "trigram_repetition": {"count": 50, "mean": 0.2218, "std": 0.1382, "min": 0.0, "q1": 0.1312, "median": 0.2355, "q3": 0.2948, "max": 0.5319, "iqr": 0.1636, "lower_bound": -0.1141, "upper_bound": 0.5401, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "3-gram重复度(反映文本短语重复程度和表达多样性)：有3-gram重复度超过30.0%的样本，短语重复度过高"}, "mtld_score": {"count": 41, "mean": 28.3798, "std": 6.2785, "min": 17.5, "q1": 25.1667, "median": 27.4286, "q3": 31.7143, "max": 43.6667, "iqr": 6.5476, "lower_bound": 15.3452, "upper_bound": 41.5357, "lower_outlier_count": 0, "upper_outlier_count": 2, "insight": "MTLD词汇多样性(反映文本词汇丰富度和表达复杂性)：有词汇多样性低于30.0的样本；词汇多样性普遍不足，可能是重复文本或词汇表达有限"}}}, "anomaly_stats": {"system": {"special_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "non_standard_spaces_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "control_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "format_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}}, "prompt": {"special_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "non_standard_spaces_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "control_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "format_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}}}, "proportion_stats": {"language": {"total_count": 50, "proportions": {"zh": {"count": 50, "proportion": 1.0}}, "most_common": ["zh", 50], "insight": "样本全部为zh类型。"}, "encoding": {"total_count": 50, "proportions": {"utf-8": {"count": 50, "proportion": 1.0}}, "most_common": ["utf-8", 50], "insight": "样本全部为utf-8类型。"}}, "pattern_stats": {"system": {"pii_email": {"total_samples": 50, "samples_with_pattern": 16, "proportion": 0.32, "most_common_matches": [["<EMAIL>", 8], ["<EMAIL>", 7], ["<EMAIL>", 5]], "insight": "在system字段中的邮箱地址检测到16个样本匹配(占比32.0%)，高风险。"}, "pii_phone": {"total_samples": 50, "samples_with_pattern": 12, "proportion": 0.24, "most_common_matches": [["19900101123", 6], ["19771212345", 5], ["19850505234", 4]], "insight": "在system字段中的手机号码检测到12个样本匹配(占比24.0%)，高风险。"}, "pii_id_card": {"total_samples": 50, "samples_with_pattern": 12, "proportion": 0.24, "most_common_matches": [["110101199001011234", 6], ["******************", 5], ["320102198505052345", 4]], "insight": "在system字段中的身份证号检测到12个样本匹配(占比24.0%)，高风险。"}, "pii_name": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "在system字段中的姓名未检测到匹配，该方面数据质量良好。"}, "html_tags": {"total_samples": 50, "samples_with_pattern": 7, "proportion": 0.14, "most_common_matches": [["<div>", 9], ["</div>", 9]], "insight": "在system字段中的HTML标签检测到7个样本匹配(占比14.0%)，需注意。"}, "urls": {"total_samples": 50, "samples_with_pattern": 16, "proportion": 0.32, "most_common_matches": [["demo.org", 8], ["example.com", 7], ["company.net", 4]], "insight": "在system字段中的网址链接检测到16个样本匹配(占比32.0%)，需注意。"}, "sensitive_words": {"total_samples": 50, "samples_with_pattern": 50, "proportion": 1.0, "by_type": {}, "by_level": {}, "unique_words": 0, "most_common_words": [], "insight": "在system字段中的敏感词汇检测到50个样本匹配(占比100.0%)，需注意。"}}, "prompt": {"pii_email": {"total_samples": 50, "samples_with_pattern": 16, "proportion": 0.32, "most_common_matches": [["<EMAIL>", 10], ["<EMAIL>", 9], ["<EMAIL>", 6]], "insight": "在prompt字段中的邮箱地址检测到16个样本匹配(占比32.0%)，高风险。"}, "pii_phone": {"total_samples": 50, "samples_with_pattern": 21, "proportion": 0.42, "most_common_matches": [["19850505234", 18], ["19771212345", 11], ["19900101123", 4]], "insight": "在prompt字段中的手机号码检测到21个样本匹配(占比42.0%)，高风险。"}, "pii_id_card": {"total_samples": 50, "samples_with_pattern": 21, "proportion": 0.42, "most_common_matches": [["320102198505052345", 18], ["******************", 11], ["110101199001011234", 4]], "insight": "在prompt字段中的身份证号检测到21个样本匹配(占比42.0%)，高风险。"}, "pii_name": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "在prompt字段中的姓名未检测到匹配，该方面数据质量良好。"}, "html_tags": {"total_samples": 50, "samples_with_pattern": 6, "proportion": 0.12, "most_common_matches": [["<div>", 6], ["</div>", 6]], "insight": "在prompt字段中的HTML标签检测到6个样本匹配(占比12.0%)，需注意。"}, "urls": {"total_samples": 50, "samples_with_pattern": 16, "proportion": 0.32, "most_common_matches": [["demo.org", 10], ["example.com", 9], ["company.net", 6]], "insight": "在prompt字段中的网址链接检测到16个样本匹配(占比32.0%)，需注意。"}, "sensitive_words": {"total_samples": 50, "samples_with_pattern": 50, "proportion": 1.0, "by_type": {}, "by_level": {}, "unique_words": 0, "most_common_words": [], "insight": "在prompt字段中的敏感词汇检测到50个样本匹配(占比100.0%)，需注意。"}}}, "insight_metadata": {"analysis_timestamp": "2025-08-01T09:25:24.292780", "analyzer_version": "2.0.0", "insights_embedded": true}, "cleaning_orchestration": {"orchestration_timestamp": "2025-08-01T09:25:37.498559", "operators": [{"name": "移除特殊字符", "order": 1, "reason": "system和prompt字段中存在大量敏感信息，如邮箱、手机号、身份证号等，需要移除特殊字符以减少隐私泄露风险。", "parameters": [{"name": "keep_punctuation", "value": false}, {"name": "custom_chars", "value": "0123456789"}], "clean_keys": ["system", "prompt"]}, {"name": "标准化空白字符", "order": 2, "reason": "系统和提示字段中的文本可能存在不一致的空格或换行符，标准化空白字符可以提升文本格式的一致性。", "parameters": [{"name": "replace_tabs", "value": true}, {"name": "normalize_newlines", "value": true}], "clean_keys": ["system", "prompt"]}, {"name": "长度过滤", "order": 3, "reason": "system和prompt字段中存在过短或过长的文本内容，可能影响后续处理效果，进行长度过滤可提高数据质量。", "parameters": [{"name": "min_length", "value": 50}, {"name": "max_length", "value": 800}, {"name": "unit", "value": "chars"}], "clean_keys": ["system", "prompt"]}, {"name": "敏感内容过滤", "order": 4, "reason": "system和prompt字段中检测到大量敏感词汇，使用敏感内容过滤算子可对这些内容进行脱敏处理，避免隐私泄露。", "parameters": [{"name": "action", "value": "mask"}, {"name": "mask_char", "value": "*"}], "clean_keys": ["system", "prompt"]}, {"name": "异常值移除", "order": 5, "reason": "system和prompt字段中存在统计上的异常样本，例如字符数异常多或少，通过IQR方法移除异常样本可以提升整体数据一致性。", "parameters": [{"name": "method", "value": "iqr"}, {"name": "threshold", "value": 1.5}], "clean_keys": ["system", "prompt"]}]}}