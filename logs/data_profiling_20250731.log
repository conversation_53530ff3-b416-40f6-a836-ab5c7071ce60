2025-07-31 14:49:26 | INFO | info:63 | ============================================================
2025-07-31 14:49:26 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 14:49:26 | INFO | info:63 | ============================================================
2025-07-31 14:49:26 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 14:49:26 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 14:49:26 | INFO | info:63 | [main]: 📋 分析字段: ['user_message', 'assistant_message', 'context']
2025-07-31 14:49:26 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 14:49:26 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 14:49:26 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 14:49:26 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 14:49:26 | INFO | info:63 | [main]: 分析字段: ['user_message', 'assistant_message', 'context']
2025-07-31 14:49:26 | WARNING | warning:67 | [main]: 警告: 以下字段在数据集中不存在: ['user_message', 'assistant_message', 'context']
2025-07-31 14:49:26 | INFO | info:63 | [main]: 实际分析字段: []
2025-07-31 14:49:26 | ERROR | error:71 | [main]: 数据分析流程失败: 没有找到可分析的字符串字段
2025-07-31 14:49:26 | ERROR | error:71 | 示例运行失败: 没有找到可分析的字符串字段
2025-07-31 14:50:00 | INFO | info:63 | ============================================================
2025-07-31 14:50:00 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 14:50:00 | INFO | info:63 | ============================================================
2025-07-31 14:50:00 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 14:50:00 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 14:50:00 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 14:50:00 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 14:50:00 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 14:50:00 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 14:50:00 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 14:50:00 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 14:50:00 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:50:00 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 14:50:00 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:50:01 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 language 的占比统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 encoding 的占比统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-07-31 14:50:01 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-07-31 14:50:01 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-07-31 14:50:01 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250731_145000.json
2025-07-31 14:50:01 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-07-31 14:50:01 | INFO | info:63 | [main]: 总样本数: 50
2025-07-31 14:50:01 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 14:50:01 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-07-31 14:50:01 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-07-31 14:50:01 | INFO | info:63 | [main]: 语言分布:
2025-07-31 14:50:01 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-07-31 14:50:01 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:50:01 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-07-31 14:50:01 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:50:01 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-07-31 14:50:01 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-07-31 14:50:01 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-07-31 14:50:01 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-07-31 14:50:01 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-07-31 14:50:01 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-07-31 14:50:02 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250731145002945777797K7w8WcMQ)","type":"mole_api_error"}}
2025-07-31 14:50:02 | ERROR | error:71 | [llm_client] 任务（未指定）：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-07-31 14:50:02 | WARNING | warning:67 | [llm_analyzer] 任务（未指定）：LLM返回结果解析失败，使用智能兜底策略
2025-07-31 14:50:02 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-07-31 14:50:02 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-07-31 14:50:02 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250731_145000.json
2025-07-31 14:50:02 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-07-31 14:50:02 | INFO | info:63 | [main]: 分析时间: N/A
2025-07-31 14:50:02 | INFO | info:63 | [main]: 总样本数: 0
2025-07-31 14:50:02 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:50:02 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-07-31 14:50:02 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:50:02 | ERROR | error:71 | [main]: 算子编排生成失败: 算子配置文件不存在: /Users/<USER>/Dev/data_profiling/operators/assets/operators.json
2025-07-31 14:50:02 | ERROR | error:71 | [main]: 数据分析流程失败: 算子配置文件不存在: /Users/<USER>/Dev/data_profiling/operators/assets/operators.json
2025-07-31 14:50:02 | ERROR | error:71 | 示例运行失败: 算子配置文件不存在: /Users/<USER>/Dev/data_profiling/operators/assets/operators.json
2025-07-31 14:51:12 | INFO | info:63 | ============================================================
2025-07-31 14:51:12 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 14:51:12 | INFO | info:63 | ============================================================
2025-07-31 14:51:12 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 14:51:12 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 14:51:12 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 14:51:12 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 14:51:12 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 14:51:12 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 14:51:12 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 14:51:12 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 14:51:12 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:51:12 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 14:51:12 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:51:13 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 language 的占比统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 encoding 的占比统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-07-31 14:51:13 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-07-31 14:51:13 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-07-31 14:51:13 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250731_145112.json
2025-07-31 14:51:13 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-07-31 14:51:13 | INFO | info:63 | [main]: 总样本数: 50
2025-07-31 14:51:13 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 14:51:13 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-07-31 14:51:13 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-07-31 14:51:13 | INFO | info:63 | [main]: 语言分布:
2025-07-31 14:51:13 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-07-31 14:51:13 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:51:13 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-07-31 14:51:13 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:51:13 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-07-31 14:51:13 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-07-31 14:51:13 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-07-31 14:51:13 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-07-31 14:51:13 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-07-31 14:51:13 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-07-31 14:51:13 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250731145113916982177NruGJzIa)","type":"mole_api_error"}}
2025-07-31 14:51:13 | ERROR | error:71 | [llm_client] 任务（未指定）：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-07-31 14:51:13 | WARNING | warning:67 | [llm_analyzer] 任务（未指定）：LLM返回结果解析失败，使用智能兜底策略
2025-07-31 14:51:13 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-07-31 14:51:13 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-07-31 14:51:13 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250731_145112.json
2025-07-31 14:51:13 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-07-31 14:51:13 | INFO | info:63 | [main]: 分析时间: N/A
2025-07-31 14:51:13 | INFO | info:63 | [main]: 总样本数: 0
2025-07-31 14:51:13 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:51:13 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-07-31 14:51:13 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:51:13 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-07-31 14:51:13 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-07-31 14:51:13 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-07-31 14:51:16 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250731145116145248294xQI7xmCI)","type":"mole_api_error"}}
2025-07-31 14:51:16 | ERROR | error:71 | [main]: 算子编排生成失败: LLM响应中未找到有效的JSON格式
2025-07-31 14:51:16 | ERROR | error:71 | [main]: 数据分析流程失败: LLM响应中未找到有效的JSON格式
2025-07-31 14:51:16 | ERROR | error:71 | 示例运行失败: LLM响应中未找到有效的JSON格式
2025-07-31 14:51:54 | INFO | info:63 | ============================================================
2025-07-31 14:51:54 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 14:51:54 | INFO | info:63 | ============================================================
2025-07-31 14:51:54 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 14:51:54 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 14:51:54 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 14:51:54 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 14:51:54 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 14:51:54 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 14:51:54 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 14:51:54 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 14:51:54 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:51:54 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 14:51:54 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:51:54 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 language 的占比统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 encoding 的占比统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-07-31 14:51:54 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-07-31 14:51:55 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-07-31 14:51:55 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-07-31 14:51:55 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-07-31 14:51:55 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-07-31 14:51:55 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-07-31 14:51:55 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-07-31 14:51:55 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-07-31 14:51:55 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-07-31 14:51:55 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-07-31 14:51:55 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-07-31 14:51:55 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-07-31 14:51:55 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-07-31 14:51:55 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-07-31 14:51:55 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250731_145154.json
2025-07-31 14:51:55 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-07-31 14:51:55 | INFO | info:63 | [main]: 总样本数: 50
2025-07-31 14:51:55 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 14:51:55 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-07-31 14:51:55 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-07-31 14:51:55 | INFO | info:63 | [main]: 语言分布:
2025-07-31 14:51:55 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-07-31 14:51:55 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:51:55 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-07-31 14:51:55 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:51:55 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-07-31 14:51:55 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-07-31 14:51:55 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-07-31 14:51:55 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-07-31 14:51:55 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-07-31 14:51:55 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-07-31 14:51:55 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250731145155598805948U3ACHXOo)","type":"mole_api_error"}}
2025-07-31 14:51:55 | ERROR | error:71 | [llm_client] 任务（未指定）：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-07-31 14:51:55 | WARNING | warning:67 | [llm_analyzer] 任务（未指定）：LLM返回结果解析失败，使用智能兜底策略
2025-07-31 14:51:55 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-07-31 14:51:55 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-07-31 14:51:55 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250731_145154.json
2025-07-31 14:51:55 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-07-31 14:51:55 | INFO | info:63 | [main]: 分析时间: N/A
2025-07-31 14:51:55 | INFO | info:63 | [main]: 总样本数: 0
2025-07-31 14:51:55 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:51:55 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-07-31 14:51:55 | INFO | info:63 | [main]: ==================================================
2025-07-31 14:51:55 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-07-31 14:51:55 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-07-31 14:51:55 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-07-31 14:51:56 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250731145156163634824xs8SoyOt)","type":"mole_api_error"}}
2025-07-31 14:51:56 | ERROR | error:71 | [main]: 算子编排生成失败: LLM响应中未找到有效的JSON格式
2025-07-31 14:51:56 | ERROR | error:71 | [main]: 数据分析流程失败: LLM响应中未找到有效的JSON格式
2025-07-31 14:51:56 | ERROR | error:71 | 示例运行失败: LLM响应中未找到有效的JSON格式
2025-07-31 17:18:04 | INFO | info:63 | ============================================================
2025-07-31 17:18:04 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 17:18:04 | INFO | info:63 | ============================================================
2025-07-31 17:18:04 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 17:18:04 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 17:18:04 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 17:18:04 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 17:18:04 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 17:18:04 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 17:18:04 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 17:18:04 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 17:18:04 | INFO | info:63 | [main]: ==================================================
2025-07-31 17:18:04 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 17:18:04 | INFO | info:63 | [main]: ==================================================
2025-07-31 17:18:05 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 language 的占比统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 encoding 的占比统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-07-31 17:18:05 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-07-31 17:18:05 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-07-31 17:18:05 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250731_171804.json
2025-07-31 17:18:05 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-07-31 17:18:05 | INFO | info:63 | [main]: 总样本数: 50
2025-07-31 17:18:05 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 17:18:05 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-07-31 17:18:05 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-07-31 17:18:05 | INFO | info:63 | [main]: 语言分布:
2025-07-31 17:18:05 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-07-31 17:18:05 | INFO | info:63 | [main]: ==================================================
2025-07-31 17:18:05 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-07-31 17:18:05 | INFO | info:63 | [main]: ==================================================
2025-07-31 17:18:05 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-07-31 17:18:05 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-07-31 17:18:05 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-07-31 17:18:05 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-07-31 17:18:05 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-07-31 17:18:05 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-07-31 17:19:06 | ERROR | error:71 | [llm_client] 任务（未指定）：调用LLM API时出错: HTTPSConnectionPool(host='api.moleapi.com', port=443): Read timed out. (read timeout=60)
2025-07-31 17:19:06 | ERROR | error:71 | [llm_client] 任务（未指定）：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-07-31 17:19:06 | WARNING | warning:67 | [llm_analyzer] 任务（未指定）：LLM返回结果解析失败，使用智能兜底策略
2025-07-31 17:19:06 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-07-31 17:19:06 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-07-31 17:19:06 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250731_171804.json
2025-07-31 17:19:06 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-07-31 17:19:06 | INFO | info:63 | [main]: 分析时间: N/A
2025-07-31 17:19:06 | INFO | info:63 | [main]: 总样本数: 0
2025-07-31 17:19:06 | INFO | info:63 | [main]: ==================================================
2025-07-31 17:19:06 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-07-31 17:19:06 | INFO | info:63 | [main]: ==================================================
2025-07-31 17:19:06 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-07-31 17:19:06 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-07-31 17:19:06 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-07-31 17:20:06 | ERROR | error:71 | [llm_client] 任务（未指定）：调用LLM API时出错: HTTPSConnectionPool(host='api.moleapi.com', port=443): Read timed out. (read timeout=60)
2025-07-31 17:20:06 | ERROR | error:71 | [main]: 算子编排生成失败: LLM响应中未找到有效的JSON格式
2025-07-31 17:20:06 | ERROR | error:71 | [main]: 数据分析流程失败: LLM响应中未找到有效的JSON格式
2025-07-31 17:20:06 | ERROR | error:71 | 示例运行失败: LLM响应中未找到有效的JSON格式
2025-07-31 18:32:20 | INFO | info:63 | ============================================================
2025-07-31 18:32:20 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 18:32:20 | INFO | info:63 | ============================================================
2025-07-31 18:32:20 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 18:32:20 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:32:20 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 18:32:20 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 18:32:20 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:32:20 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 18:32:20 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 18:32:20 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 18:32:20 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:32:20 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 18:32:20 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:32:21 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 18:32:21 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 18:32:21 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 18:32:21 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 18:32:21 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 18:32:21 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 18:32:21 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 18:32:21 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 18:33:37 | INFO | info:63 | ============================================================
2025-07-31 18:33:37 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 18:33:37 | INFO | info:63 | ============================================================
2025-07-31 18:33:37 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 18:33:37 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:33:37 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 18:33:37 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 18:33:37 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:33:37 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 18:33:37 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 18:33:37 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 18:33:37 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:33:37 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 18:33:37 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:33:37 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 18:33:37 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 18:33:37 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 18:33:37 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 18:33:37 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 18:33:37 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 18:33:37 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 18:33:37 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 18:34:03 | INFO | info:63 | ============================================================
2025-07-31 18:34:03 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 18:34:03 | INFO | info:63 | ============================================================
2025-07-31 18:34:03 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 18:34:03 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:34:03 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 18:34:03 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 18:34:03 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:34:03 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 18:34:03 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 18:34:03 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 18:34:03 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:34:03 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 18:34:03 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:34:52 | INFO | info:63 | ============================================================
2025-07-31 18:34:52 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 18:34:52 | INFO | info:63 | ============================================================
2025-07-31 18:34:52 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 18:34:52 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:34:52 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 18:34:52 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 18:34:52 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:34:52 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 18:34:52 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 18:34:52 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 18:34:52 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:34:52 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 18:34:52 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:36:28 | INFO | info:63 | ============================================================
2025-07-31 18:36:28 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 18:36:28 | INFO | info:63 | ============================================================
2025-07-31 18:36:28 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 18:36:28 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:36:28 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 18:36:28 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 18:36:28 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:36:28 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 18:36:28 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 18:36:28 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 18:36:28 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:36:28 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 18:36:28 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:40:32 | INFO | info:63 | ============================================================
2025-07-31 18:40:32 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 18:40:32 | INFO | info:63 | ============================================================
2025-07-31 18:40:32 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 18:40:32 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:40:32 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 18:40:32 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 18:40:32 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:40:32 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 18:40:32 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 18:40:32 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 18:40:32 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:40:32 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 18:40:32 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:40:32 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 18:40:32 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 18:40:32 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 18:40:32 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 18:40:32 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 18:40:32 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 18:40:32 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 18:40:32 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 18:40:53 | INFO | info:63 | ============================================================
2025-07-31 18:40:53 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 18:40:53 | INFO | info:63 | ============================================================
2025-07-31 18:40:53 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 18:40:53 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:40:53 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 18:40:53 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 18:40:53 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:40:53 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 18:40:53 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 18:40:53 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 18:40:53 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:40:53 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 18:40:53 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:40:54 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 18:40:54 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 18:40:54 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 18:40:54 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 18:40:54 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 18:40:54 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 18:40:54 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 18:40:54 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-07-31 18:40:57 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 language 的占比统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 encoding 的占比统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-07-31 18:41:28 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-07-31 18:41:28 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-07-31 18:41:28 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250731_184053.json
2025-07-31 18:41:28 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-07-31 18:41:28 | INFO | info:63 | [main]: 总样本数: 50
2025-07-31 18:41:28 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 18:41:28 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-07-31 18:41:28 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-07-31 18:41:28 | INFO | info:63 | [main]: 语言分布:
2025-07-31 18:41:28 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-07-31 18:41:28 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:41:28 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-07-31 18:41:28 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:41:28 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-07-31 18:41:28 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-07-31 18:41:28 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-07-31 18:41:28 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-07-31 18:41:28 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-07-31 18:41:28 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-07-31 18:41:29 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250731184129714757591acjvp3h6)","type":"mole_api_error"}}
2025-07-31 18:41:29 | ERROR | error:71 | [llm_client] 任务（未指定）：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-07-31 18:41:29 | WARNING | warning:67 | [llm_analyzer] 任务（未指定）：LLM返回结果解析失败，使用智能兜底策略
2025-07-31 18:41:29 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-07-31 18:41:29 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-07-31 18:41:29 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250731_184053.json
2025-07-31 18:41:29 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-07-31 18:41:29 | INFO | info:63 | [main]: 分析时间: N/A
2025-07-31 18:41:29 | INFO | info:63 | [main]: 总样本数: 0
2025-07-31 18:41:29 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:41:29 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-07-31 18:41:29 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:41:29 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-07-31 18:41:29 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-07-31 18:41:29 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-07-31 18:41:30 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250731184130154195803nGqQXWRk)","type":"mole_api_error"}}
2025-07-31 18:41:30 | ERROR | error:71 | [main]: 算子编排生成失败: LLM响应中未找到有效的JSON格式
2025-07-31 18:41:30 | ERROR | error:71 | [main]: 数据分析流程失败: LLM响应中未找到有效的JSON格式
2025-07-31 18:41:30 | ERROR | error:71 | 示例运行失败: LLM响应中未找到有效的JSON格式
2025-07-31 18:42:53 | INFO | info:63 | ============================================================
2025-07-31 18:42:53 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 18:42:53 | INFO | info:63 | ============================================================
2025-07-31 18:42:53 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 18:42:53 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:42:53 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 18:42:53 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 18:42:53 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:42:53 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 18:42:53 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 18:42:53 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 18:42:53 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:42:53 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 18:42:53 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:42:54 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 18:42:54 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 18:42:54 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 18:42:54 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 18:42:54 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 18:42:54 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 18:42:54 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 18:42:54 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-07-31 18:42:56 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-07-31 18:43:36 | INFO | info:63 | ============================================================
2025-07-31 18:43:36 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 18:43:36 | INFO | info:63 | ============================================================
2025-07-31 18:43:36 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 18:43:36 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:43:36 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 18:43:36 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 18:43:36 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 18:43:36 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 18:43:36 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 18:43:36 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 18:43:36 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:43:36 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 18:43:36 | INFO | info:63 | [main]: ==================================================
2025-07-31 18:43:36 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 19:32:42 | INFO | info:63 | ============================================================
2025-07-31 19:32:42 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 19:32:42 | INFO | info:63 | ============================================================
2025-07-31 19:32:42 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 19:32:42 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 19:32:42 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 19:32:42 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 19:32:42 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 19:32:42 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 19:32:42 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 19:32:42 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 19:32:42 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:32:42 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 19:32:42 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:32:43 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 language 的占比统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 encoding 的占比统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-07-31 19:32:43 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-07-31 19:32:43 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-07-31 19:32:43 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250731_193242.json
2025-07-31 19:32:43 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-07-31 19:32:43 | INFO | info:63 | [main]: 总样本数: 50
2025-07-31 19:32:43 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 19:32:43 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-07-31 19:32:43 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-07-31 19:32:43 | INFO | info:63 | [main]: 语言分布:
2025-07-31 19:32:43 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-07-31 19:32:43 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:32:43 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-07-31 19:32:43 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:32:43 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-07-31 19:32:43 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-07-31 19:32:43 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-07-31 19:32:43 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-07-31 19:32:43 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-07-31 19:32:43 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-07-31 19:32:44 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250731193244376736909WnfnbLiA)","type":"mole_api_error"}}
2025-07-31 19:32:44 | ERROR | error:71 | [llm_client] 任务（未指定）：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-07-31 19:32:44 | WARNING | warning:67 | [llm_analyzer] 任务（未指定）：LLM返回结果解析失败，使用智能兜底策略
2025-07-31 19:32:44 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-07-31 19:32:44 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-07-31 19:32:44 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250731_193242.json
2025-07-31 19:32:44 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-07-31 19:32:44 | INFO | info:63 | [main]: 分析时间: N/A
2025-07-31 19:32:44 | INFO | info:63 | [main]: 总样本数: 0
2025-07-31 19:32:44 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:32:44 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-07-31 19:32:44 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:32:44 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-07-31 19:32:44 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-07-31 19:32:44 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-07-31 19:32:44 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 202507311932448581536444d329Iqn)","type":"mole_api_error"}}
2025-07-31 19:32:44 | ERROR | error:71 | [main]: 算子编排生成失败: LLM响应中未找到有效的JSON格式
2025-07-31 19:32:44 | ERROR | error:71 | [main]: 数据分析流程失败: LLM响应中未找到有效的JSON格式
2025-07-31 19:32:44 | ERROR | error:71 | 示例运行失败: LLM响应中未找到有效的JSON格式
2025-07-31 19:35:44 | INFO | info:63 | ============================================================
2025-07-31 19:35:44 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 19:35:44 | INFO | info:63 | ============================================================
2025-07-31 19:35:44 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 19:35:44 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 19:35:44 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 19:35:44 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 19:35:44 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 19:35:44 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 19:35:44 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 19:35:44 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 19:35:44 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:35:44 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 19:35:44 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:35:45 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 language 的占比统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 encoding 的占比统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-07-31 19:35:45 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-07-31 19:35:45 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-07-31 19:35:45 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250731_193544.json
2025-07-31 19:35:45 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-07-31 19:35:45 | INFO | info:63 | [main]: 总样本数: 50
2025-07-31 19:35:45 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 19:35:45 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-07-31 19:35:45 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-07-31 19:35:45 | INFO | info:63 | [main]: 语言分布:
2025-07-31 19:35:45 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-07-31 19:35:45 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:35:45 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-07-31 19:35:45 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:35:45 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-07-31 19:35:45 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-07-31 19:35:45 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-07-31 19:35:45 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-07-31 19:35:45 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-07-31 19:35:45 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-07-31 19:35:46 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250731193546154903520uPxtP0WC)","type":"mole_api_error"}}
2025-07-31 19:35:46 | ERROR | error:71 | [llm_client] 任务（未指定）：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-07-31 19:35:46 | WARNING | warning:67 | [llm_analyzer] 任务（未指定）：LLM返回结果解析失败，使用智能兜底策略
2025-07-31 19:35:46 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-07-31 19:35:46 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-07-31 19:35:46 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250731_193544.json
2025-07-31 19:35:46 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-07-31 19:35:46 | INFO | info:63 | [main]: 分析时间: N/A
2025-07-31 19:35:46 | INFO | info:63 | [main]: 总样本数: 0
2025-07-31 19:35:46 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:35:46 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-07-31 19:35:46 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:35:46 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-07-31 19:35:46 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-07-31 19:35:46 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-07-31 19:35:46 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250731193546623696797AAnPh4WS)","type":"mole_api_error"}}
2025-07-31 19:35:46 | ERROR | error:71 | [main]: 算子编排生成失败: LLM响应中未找到有效的JSON格式
2025-07-31 19:35:46 | ERROR | error:71 | [main]: 数据分析流程失败: LLM响应中未找到有效的JSON格式
2025-07-31 19:35:46 | ERROR | error:71 | 示例运行失败: LLM响应中未找到有效的JSON格式
2025-07-31 19:36:16 | INFO | info:63 | ============================================================
2025-07-31 19:36:16 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 19:36:16 | INFO | info:63 | ============================================================
2025-07-31 19:36:16 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 19:36:16 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 19:36:16 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 19:36:16 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 19:36:16 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 19:36:16 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 19:36:16 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 19:36:16 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 19:36:16 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:36:16 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 19:36:16 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:36:17 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 language 的占比统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 encoding 的占比统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-07-31 19:36:17 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-07-31 19:36:17 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-07-31 19:36:17 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250731_193616.json
2025-07-31 19:36:17 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-07-31 19:36:17 | INFO | info:63 | [main]: 总样本数: 50
2025-07-31 19:36:17 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 19:36:17 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-07-31 19:36:17 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-07-31 19:36:17 | INFO | info:63 | [main]: 语言分布:
2025-07-31 19:36:17 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-07-31 19:36:17 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:36:17 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-07-31 19:36:17 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:36:17 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-07-31 19:36:17 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-07-31 19:36:17 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-07-31 19:36:17 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-07-31 19:36:17 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-07-31 19:36:17 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-07-31 19:36:17 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 202507311936179678767198phhh876)","type":"mole_api_error"}}
2025-07-31 19:36:17 | ERROR | error:71 | [llm_client] 任务（未指定）：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-07-31 19:36:17 | WARNING | warning:67 | [llm_analyzer] 任务（未指定）：LLM返回结果解析失败，使用智能兜底策略
2025-07-31 19:36:17 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-07-31 19:36:17 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-07-31 19:36:17 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250731_193616.json
2025-07-31 19:36:17 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-07-31 19:36:17 | INFO | info:63 | [main]: 分析时间: N/A
2025-07-31 19:36:17 | INFO | info:63 | [main]: 总样本数: 0
2025-07-31 19:36:17 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:36:17 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-07-31 19:36:17 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:36:17 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-07-31 19:36:17 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-07-31 19:36:17 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-07-31 19:36:18 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250731193618536188621ODrYZTNW)","type":"mole_api_error"}}
2025-07-31 19:36:18 | ERROR | error:71 | [main]: 算子编排生成失败: LLM响应中未找到有效的JSON格式
2025-07-31 19:36:18 | ERROR | error:71 | [main]: 数据分析流程失败: LLM响应中未找到有效的JSON格式
2025-07-31 19:36:18 | ERROR | error:71 | 示例运行失败: LLM响应中未找到有效的JSON格式
2025-07-31 19:42:07 | INFO | info:63 | ============================================================
2025-07-31 19:42:07 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 19:42:07 | INFO | info:63 | ============================================================
2025-07-31 19:42:07 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 19:42:07 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 19:42:07 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 19:42:07 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 19:42:07 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 19:42:07 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 19:42:07 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 19:42:07 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 19:42:07 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:42:07 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 19:42:07 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:42:07 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 language 的占比统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 encoding 的占比统计
2025-07-31 19:42:07 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-07-31 19:42:08 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-07-31 19:42:08 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-07-31 19:42:08 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-07-31 19:42:08 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-07-31 19:42:08 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-07-31 19:42:08 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-07-31 19:42:08 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-07-31 19:42:08 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-07-31 19:42:08 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-07-31 19:42:08 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-07-31 19:42:08 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-07-31 19:42:08 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-07-31 19:42:08 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-07-31 19:42:08 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-07-31 19:42:08 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250731_194207.json
2025-07-31 19:42:08 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-07-31 19:42:08 | INFO | info:63 | [main]: 总样本数: 50
2025-07-31 19:42:08 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 19:42:08 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-07-31 19:42:08 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-07-31 19:42:08 | INFO | info:63 | [main]: 语言分布:
2025-07-31 19:42:08 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-07-31 19:42:08 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:42:08 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-07-31 19:42:08 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:42:08 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-07-31 19:42:08 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-07-31 19:42:08 | ERROR | error:71 | [main]: 数据洞察报告生成失败: name 'anlysis_dimensions' is not defined
2025-07-31 19:42:08 | ERROR | error:71 | [main]: 数据分析流程失败: name 'anlysis_dimensions' is not defined
2025-07-31 19:42:08 | ERROR | error:71 | 示例运行失败: name 'anlysis_dimensions' is not defined
2025-07-31 19:42:12 | INFO | info:63 | ============================================================
2025-07-31 19:42:12 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 19:42:12 | INFO | info:63 | ============================================================
2025-07-31 19:42:12 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 19:42:12 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 19:42:12 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 19:42:12 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 19:42:12 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 19:42:12 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 19:42:12 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 19:42:12 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 19:42:12 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:42:12 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 19:42:12 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:42:12 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 language 的占比统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 encoding 的占比统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-07-31 19:42:12 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-07-31 19:42:13 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-07-31 19:42:13 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-07-31 19:42:13 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-07-31 19:42:13 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-07-31 19:42:13 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-07-31 19:42:13 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-07-31 19:42:13 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-07-31 19:42:13 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-07-31 19:42:13 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250731_194212.json
2025-07-31 19:42:13 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-07-31 19:42:13 | INFO | info:63 | [main]: 总样本数: 50
2025-07-31 19:42:13 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 19:42:13 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-07-31 19:42:13 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-07-31 19:42:13 | INFO | info:63 | [main]: 语言分布:
2025-07-31 19:42:13 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-07-31 19:42:13 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:42:13 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-07-31 19:42:13 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:42:13 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-07-31 19:42:13 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-07-31 19:43:54 | INFO | info:63 | ============================================================
2025-07-31 19:43:54 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 19:43:54 | INFO | info:63 | ============================================================
2025-07-31 19:43:54 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 19:43:54 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 19:43:54 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 19:43:54 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 19:43:54 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 19:43:54 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 19:43:54 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 19:43:54 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 19:43:54 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:43:54 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 19:43:54 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:43:55 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 language 的占比统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 encoding 的占比统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-07-31 19:43:55 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-07-31 19:43:55 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-07-31 19:43:55 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250731_194354.json
2025-07-31 19:43:55 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-07-31 19:43:55 | INFO | info:63 | [main]: 总样本数: 50
2025-07-31 19:43:55 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 19:43:55 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-07-31 19:43:55 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-07-31 19:43:55 | INFO | info:63 | [main]: 语言分布:
2025-07-31 19:43:55 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-07-31 19:43:55 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:43:55 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-07-31 19:43:55 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:43:55 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-07-31 19:43:55 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-07-31 19:48:46 | INFO | info:63 | ============================================================
2025-07-31 19:48:46 | INFO | info:63 | 开始运行数据分析流水线示例
2025-07-31 19:48:46 | INFO | info:63 | ============================================================
2025-07-31 19:48:46 | INFO | info:63 | [main]: 🚀 开始数据分析流程
2025-07-31 19:48:46 | INFO | info:63 | [main]: 📁 数据文件: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 19:48:46 | INFO | info:63 | [main]: 📋 分析字段: ['system', 'prompt']
2025-07-31 19:48:46 | INFO | info:63 | [main]: 📂 报告目录: report
2025-07-31 19:48:46 | INFO | info:63 | [main]: 正在加载数据集: data/test/test_data_fine_tuning_50_samples.jsonl
2025-07-31 19:48:46 | INFO | info:63 | [main]: 成功加载数据集，共 50 个样本
2025-07-31 19:48:46 | INFO | info:63 | [main]: 数据集字段: ['id', 'generated_at', 'system', 'system_length', 'prompt', 'prompt_length', 'response', 'response_length']
2025-07-31 19:48:46 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 19:48:46 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:48:46 | INFO | info:63 | [main]: 步骤1: 生成数据画像报告
2025-07-31 19:48:46 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:48:46 | INFO | log:45 | 开始分析数据集画像...
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.char_count 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.alpha_ratio 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.digit_ratio 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.alnum_ratio 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.valid_ratio 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.word_count 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.stopword_count 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.avg_word_length 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.sentence_count 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.line_count 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.avg_sentence_length 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.avg_line_length 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.bigram_repetition 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.trigram_repetition 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 system.mtld_score 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 prompt.char_count 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 prompt.alpha_ratio 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 prompt.digit_ratio 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 prompt.alnum_ratio 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 prompt.valid_ratio 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 prompt.word_count 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 prompt.stopword_count 的箱图统计
2025-07-31 19:48:46 | INFO | log:45 | 完成 prompt.avg_word_length 的箱图统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.sentence_count 的箱图统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.line_count 的箱图统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.avg_sentence_length 的箱图统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.avg_line_length 的箱图统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.bigram_repetition 的箱图统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.trigram_repetition 的箱图统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.mtld_score 的箱图统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 system.special_ratio 的异常统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 system.non_standard_spaces_ratio 的异常统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 system.control_chars_ratio 的异常统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 system.format_chars_ratio 的异常统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.special_ratio 的异常统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.non_standard_spaces_ratio 的异常统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.control_chars_ratio 的异常统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.format_chars_ratio 的异常统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 language 的占比统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 encoding 的占比统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 system.pii_email 的模式匹配统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 system.pii_phone 的模式匹配统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 system.pii_id_card 的模式匹配统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 system.pii_name 的模式匹配统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 system.html_tags 的模式匹配统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 system.urls 的模式匹配统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 system.sensitive_words 的模式匹配统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.pii_email 的模式匹配统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.pii_phone 的模式匹配统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.pii_id_card 的模式匹配统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.pii_name 的模式匹配统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.html_tags 的模式匹配统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.urls 的模式匹配统计
2025-07-31 19:48:47 | INFO | log:45 | 完成 prompt.sensitive_words 的模式匹配统计
2025-07-31 19:48:47 | INFO | info:63 | [main]: 数据画像报告生成完成
2025-07-31 19:48:47 | INFO | info:63 | [main]: 报告保存路径: report/data_profile_report_20250731_194846.json
2025-07-31 19:48:47 | INFO | info:63 | [main]: 数据画像报告摘要:
2025-07-31 19:48:47 | INFO | info:63 | [main]: 总样本数: 50
2025-07-31 19:48:47 | INFO | info:63 | [main]: 分析字段: ['system', 'prompt']
2025-07-31 19:48:47 | INFO | info:63 | [main]: 平均文本长度: 262.69 字符
2025-07-31 19:48:47 | INFO | info:63 | [main]: 内容多样性分数: 28.41
2025-07-31 19:48:47 | INFO | info:63 | [main]: 语言分布:
2025-07-31 19:48:47 | INFO | info:63 | [main]:   zh: 50 样本 (100.0%)
2025-07-31 19:48:47 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:48:47 | INFO | info:63 | [main]: 步骤2: 生成数据洞察报告
2025-07-31 19:48:47 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:48:47 | INFO | info:63 | [insight_core]：开始为数据画像报告添加洞察结论...
2025-07-31 19:48:47 | INFO | info:63 | [insight_core]：步骤1: 为箱图统计指标添加洞察...
2025-07-31 19:48:48 | INFO | info:63 | [insight_core]：步骤2: 为异常检测指标添加洞察...
2025-07-31 19:48:48 | INFO | info:63 | [insight_core]：步骤3: 为占比统计指标添加洞察...
2025-07-31 19:48:48 | INFO | info:63 | [insight_core]：步骤4: 分析模式匹配指标和领域洞察...
2025-07-31 19:48:48 | INFO | info:63 | [llm_analyzer] 任务（未指定）：开始分析模式匹配指标和领域洞察...
2025-07-31 19:48:49 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250731194849632306156YeWYaHlv)","type":"mole_api_error"}}
2025-07-31 19:48:49 | ERROR | error:71 | [llm_client] 任务（未指定）：解析JSON响应时出错: Expecting value: line 1 column 1 (char 0)
2025-07-31 19:48:49 | WARNING | warning:67 | [llm_analyzer] 任务（未指定）：LLM返回结果解析失败，使用智能兜底策略
2025-07-31 19:48:49 | INFO | info:63 | [llm_analyzer] 任务（未指定）：分析完成
2025-07-31 19:48:49 | INFO | info:63 | [main]: 数据洞察报告生成完成
2025-07-31 19:48:49 | INFO | info:63 | [main]: 报告保存路径: report/data_insight_report_20250731_194846.json
2025-07-31 19:48:49 | INFO | info:63 | [main]: 数据洞察报告摘要:
2025-07-31 19:48:49 | INFO | info:63 | [main]: 分析时间: N/A
2025-07-31 19:48:49 | INFO | info:63 | [main]: 总样本数: 0
2025-07-31 19:48:49 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:48:49 | INFO | info:63 | [main]: 步骤3: 生成算子编排
2025-07-31 19:48:49 | INFO | info:63 | [main]: ==================================================
2025-07-31 19:48:49 | INFO | info:63 | [asset_utils] 任务（未指定）：✅ 成功加载算子配置，共10个算子
2025-07-31 19:48:49 | INFO | info:63 | [orchestrator_core]：LLM客户端初始化成功
2025-07-31 19:48:49 | INFO | info:63 | [orchestrator_core]：开始分析数据洞察报告并编排清洗算子...
2025-07-31 19:48:50 | ERROR | error:71 | [llm_client] 任务（未指定）：API调用失败: 503, {"error":{"message":"当前分组 default 下对于模型  无可用渠道 (request id: 20250731194850420685790Hpnqzo3W)","type":"mole_api_error"}}
2025-07-31 19:48:50 | ERROR | error:71 | [main]: 算子编排生成失败: LLM响应中未找到有效的JSON格式
2025-07-31 19:48:50 | ERROR | error:71 | [main]: 数据分析流程失败: LLM响应中未找到有效的JSON格式
2025-07-31 19:48:50 | ERROR | error:71 | 示例运行失败: LLM响应中未找到有效的JSON格式
