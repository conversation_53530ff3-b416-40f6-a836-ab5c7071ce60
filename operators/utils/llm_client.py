"""
通用LLM客户端工具类

提供统一的大模型API调用接口，直接使用全局上下文配置
"""

import json
import re
import requests
from typing import Dict, List, Any, Optional
from config.dependencies import context
from utils.logger_util import logger

class LLMClient:
    """通用LLM客户端 - 提供统一的大模型API调用接口"""

    def __init__(self):
        """
        初始化LLM客户端

        直接从全局上下文配置加载所有参数
        """
        open_llm_config = context.get('open_llm', {})

        self.api_url = open_llm_config.get('api_url')
        self.api_key = open_llm_config.get('api_key')
        self.model = open_llm_config.get('model')
        self.api_params = open_llm_config.get('api_params', {})

        self.no_think = context.get('no_think', False)

    def _remove_think_tags(self, text: str) -> str:
        """
        去除文本中的<think>和</think>标签及其内容

        Args:
            text: 包含可能的思考标签的文本

        Returns:
            去除思考标签后的文本
        """
        # 使用正则表达式去除<think>和</think>标签及其内容
        # re.DOTALL标志使.匹配包括换行符在内的任何字符
        pattern = r'<think>.*?</think>'
        cleaned_text = re.sub(pattern, '', text, flags=re.DOTALL | re.IGNORECASE)

        # 去除多余的空白行
        cleaned_text = re.sub(r'\n\s*\n', '\n', cleaned_text)

        return cleaned_text.strip()

    def call(self,
             prompt: str,
             system_message: Optional[str] = None,
             temperature: Optional[float] = None,
             top_p: Optional[float] = None,
             max_tokens: Optional[int] = None,
             timeout: int = 60) -> str:
        """
        调用LLM API

        Args:
            prompt: 用户输入提示
            system_message: 系统消息（可选）
            temperature: 温度参数，覆盖配置中的默认值
            top_p: Top P参数，覆盖配置中的默认值
            max_tokens: 最大token数，覆盖配置中的默认值
            timeout: 请求超时时间（秒）

        Returns:
            LLM响应文本，失败时返回空字符串
        """
        try:
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f"Bearer {self.api_key}"
            }

            # 处理prompt，如果no_think为True，添加"/no_think"后缀
            processed_prompt = prompt
            if self.no_think:
                processed_prompt = prompt + "/no_think"

            # 构建消息列表
            messages = []
            if system_message:
                messages.append({
                    'role': 'system',
                    'content': system_message
                })
            messages.append({
                'role': 'user',
                'content': processed_prompt
            })

            # 构建请求参数
            api_params = self.api_params.copy()
            if temperature is not None:
                api_params['temperature'] = temperature
            if top_p is not None:
                api_params['top_p'] = top_p
            if max_tokens is not None:
                api_params['max_tokens'] = max_tokens

            data = {
                'messages': messages,
                **api_params
            }
            
            response = requests.post(
                self.api_url,
                headers=headers,
                json=data,
                timeout=timeout
            )

            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']

                # 去除可能的思考内容
                content = self._remove_think_tags(content)

                return content
            else:
                logger.error(f"[llm_client]：API调用失败: {response.status_code}, {response.text}")
                return ""

        except Exception as e:
            logger.error(f"[llm_client]：调用LLM API时出错: {e}")
            return ""

    def call_json(self, 
                  prompt: str, 
                  system_message: Optional[str] = None,
                  temperature: Optional[float] = None,
                  top_p: Optional[float] = None,
                  max_tokens: Optional[int] = None,
                  timeout: int = 60) -> Optional[Dict[str, Any]]:
        """
        调用LLM API并尝试解析JSON响应

        Args:
            prompt: 用户输入提示
            system_message: 系统消息（可选）
            temperature: 温度参数，覆盖配置中的默认值
            top_p: Top P参数，覆盖配置中的默认值
            max_tokens: 最大token数，覆盖配置中的默认值
            timeout: 请求超时时间（秒）

        Returns:
            解析后的JSON字典，失败时返回None
        """
        response_text = self.call(prompt, system_message, temperature, top_p, max_tokens, timeout)
        
        if not response_text:
            return None
            
        return self.parse_json_response(response_text)

    def parse_json_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """
        解析LLM响应中的JSON内容

        Args:
            response_text: LLM响应文本

        Returns:
            解析后的JSON字典，失败时返回None
        """
        try:
            # 尝试提取JSON部分
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1

            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # 如果没有找到大括号，尝试直接解析整个响应
                return json.loads(response_text.strip())

        except Exception as e:
            logger.error(f"[llm_client]：解析JSON响应时出错: {e}")
            return None
